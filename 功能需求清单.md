# AI小说IDE 功能需求清单

## 📋 项目概述
**项目名称**: AI小说IDE (NovelCraft AI Studio)
**技术栈**: React 18 + TypeScript + Vite + Mantine (纯前端Web应用)
**界面设计**: 基于91Writing项目的现代化界面风格
**开发周期**: 12-16周
**项目复杂度**: 大型前端项目（集成7大核心系统）
**部署方式**: 静态网站部署，支持本地离线使用

## 🎯 核心功能模块

### 1. 界面系统模块 (UI System)
**描述**: 基于91Writing风格的现代化界面系统，提供5个专业工作区
**优先级**: 🔴 高优先级 (P0)

### 2. 编辑器模块 (Editor System)  
**描述**: 基于Monaco Editor的智能小说编辑器，支持AI辅助写作
**优先级**: 🔴 高优先级 (P0)

### 3. AI集成模块 (AI Integration)
**描述**: 多AI模型集成，支持30+AI服务商，智能提示词系统（纯前端API调用）
**优先级**: 🔴 高优先级 (P0)

### 4. 角色管理模块 (Character Management)
**描述**: 完整的角色管理系统，支持SillyTavern角色卡格式（本地存储）
**优先级**: 🟡 中优先级 (P1)

### 5. 知识库模块 (Knowledge Base)
**描述**: 本地向量搜索 + RAG增强 + 记忆管理系统（浏览器本地存储）
**优先级**: 🟡 中优先级 (P1)

### 6. 知识图谱模块 (Knowledge Graph)
**描述**: 本地知识图谱 + 可视化系统（移除MCP集成）
**优先级**: 🟢 低优先级 (P2)

### 7. 文本分析模块 (Text Analysis)
**描述**: 雷点检测 + 改编策略 + 批量处理引擎（前端实现）
**优先级**: 🟢 低优先级 (P2)

### 8. 工作流模块 (Workflow Engine)
**描述**: 可视化节点编辑器 + 前端任务调度系统
**优先级**: 🟢 低优先级 (P2)

### 9. SillyTavern兼容模块 (ST Compatibility)
**描述**: 本地SillyTavern扩展支持，移除扩展商店功能
**优先级**: 🟡 中优先级 (P1)

### 10. 项目管理模块 (Project Management)
**描述**: 项目创建、文件组织、本地备份、导入导出（纯本地功能）
**优先级**: 🔴 高优先级 (P0)

## 📝 具体功能点

### 1. 界面系统模块
#### 1.1 91Writing风格主题系统
- **功能描述**: 完整映射91Writing色彩系统和设计规范
- **技术要求**: Mantine主题深度定制，CSS变量管理
- **实现要点**: 
  - 主色调#409eff，成功色#67c23a，警告色#e6a23c，危险色#f56c6c
  - 文本色阶：#303133(主要)、#606266(常规)、#909399(次要)
  - 背景色：#f5f7fa(页面)、#fafafa(浅色)
  - 6px圆角，统一间距系统(8px倍数)

#### 1.2 AppShell主布局系统
- **功能描述**: 顶部导航(60px) + 三栏式主体布局(320px + 自适应 + 320px)
- **技术要求**: Mantine AppShell组件，响应式设计
- **实现要点**:
  - 顶部导航：应用标题 + 工作区切换 + 功能按钮
  - 左侧面板：功能设置、文件树、工具面板
  - 中间区域：编辑器、可视化区、配置表单
  - 右侧面板：AI助手、聊天面板、结果展示

#### 1.3 五个工作区系统
- **功能描述**: 编辑器、角色管理、知识图谱、项目管理、设置配置工作区
- **技术要求**: React组件化设计，状态管理，路由切换
- **实现要点**:
  - 工作区切换机制(Ctrl+1-5快捷键)
  - 状态保持和恢复
  - 平滑切换动画效果

#### 1.4 基础组件库
- **功能描述**: WorkspaceCard、ToolbarButton、StatusIndicator、ChatPanel等
- **技术要求**: TypeScript类型定义，Mantine组件扩展
- **实现要点**:
  - 统一的视觉风格和交互规范
  - 可复用的组件设计
  - 完整的属性接口定义

### 2. 编辑器模块
#### 2.1 Monaco Editor集成
- **功能描述**: 专业代码编辑器集成，支持小说写作优化
- **技术要求**: @monaco-editor/react，自定义语言支持
- **实现要点**:
  - 91Writing风格主题配置
  - 小说专用语法高亮
  - 智能提示和自动补全
  - 多标签页支持

#### 2.2 智能续写功能
- **功能描述**: Ctrl+J快捷键触发AI续写，多种生成模式
- **技术要求**: AI API集成，流式输出处理
- **实现要点**:
  - 续写、改写、扩展、总结四种模式
  - 实时流式输出显示
  - 生成状态指示和进度显示
  - 生成结果的接受/拒绝操作

#### 2.3 文件管理系统
- **功能描述**: 左侧文件树，支持新建、删除、重命名等操作
- **技术要求**: Mantine Tree组件，文件系统API
- **实现要点**:
  - 91Writing风格的文件树设计
  - 章节管理和自动排序
  - 文件拖拽操作
  - 自动保存和恢复机制

#### 2.4 编辑器工具栏
- **功能描述**: 参考91Writing的编辑器工具栏设计
- **技术要求**: Mantine Button组件，图标库
- **实现要点**:
  - 字数统计、预计阅读时间显示
  - 生成、导出、清空等功能按钮
  - 编辑器设置快捷入口
  - 状态栏信息显示

### 3. AI集成模块
#### 3.1 多AI模型支持
- **功能描述**: 支持OpenAI、Claude、DeepSeek、Groq、MistralAI等30+AI服务商
- **技术要求**: 统一API抽象层，适配器模式
- **实现要点**:
  - 统一的API调用封装
  - 参数映射和格式转换
  - 热切换机制
  - 负载均衡和故障转移

#### 3.2 API密钥管理
- **功能描述**: 密钥池管理，轮询机制，使用统计
- **技术要求**: 浏览器本地加密存储，前端安全管理
- **实现要点**:
  - 多密钥轮询使用
  - 使用量统计和限制
  - 密钥有效性检测
  - localStorage/IndexedDB安全存储

#### 3.3 高级参数配置
- **功能描述**: temperature、top_p、top_k等完整参数支持
- **技术要求**: 前端参数验证，本地预设管理
- **实现要点**:
  - 参数范围验证和类型检查
  - 预设的本地导入导出功能
  - 本地预设管理（移除社区分享）
  - 实时参数调整

#### 3.4 智能提示词系统
- **功能描述**: 动态提示词模板，变量替换，条件逻辑
- **技术要求**: 前端模板引擎，本地版本管理
- **实现要点**:
  - 动态变量替换
  - 模板本地版本管理和回滚
  - 本地模板管理（移除社区分享）
  - 提示词拦截器系统

### 4. 角色管理模块
#### 4.1 角色卡解析导入
- **功能描述**: 支持PNG角色卡解析，兼容SillyTavern V2格式
- **技术要求**: 前端PNG元数据解析，JSON格式处理
- **实现要点**:
  - 浏览器端PNG嵌入式角色卡解析
  - SillyTavern V2格式完全兼容
  - 批量导入功能
  - 角色卡验证和修复

#### 4.2 角色编辑器
- **功能描述**: 完整的角色属性编辑，支持Name、Description、Personality等
- **技术要求**: React表单组件，前端数据验证
- **实现要点**:
  - 所有SillyTavern字段支持
  - 实时预览功能
  - 角色头像本地管理
  - 本地属性模板系统

#### 4.3 角色关系图谱
- **功能描述**: 可视化角色关系，支持多种关系类型
- **技术要求**: Cytoscape.js，前端图谱算法
- **实现要点**:
  - 交互式关系编辑
  - 关系类型定义
  - 图谱布局算法
  - 关系分析统计

#### 4.4 世界书系统
- **功能描述**: 兼容SillyTavern World Info，支持关键词激活
- **技术要求**: 前端关键词匹配，逻辑判断
- **实现要点**:
  - 前端关键词匹配算法
  - AND、OR、NOT逻辑支持
  - 条目激活历史
  - 递归扫描机制

## 🎯 优先级分类

### 🔴 高优先级 (P0) - Week 1-4
**目标**: 基础可用的小说编辑器（纯前端）
- 界面系统模块 (完整)
- 编辑器模块 (基础功能)
- AI集成模块 (基础功能)
- 项目管理模块 (基础功能)

### 🟡 中优先级 (P1) - Week 5-10
**目标**: 完整的AI辅助写作系统
- 角色管理模块 (完整)
- 知识库模块 (完整)
- SillyTavern兼容模块 (完整)
- AI集成模块 (高级功能)

### 🟢 低优先级 (P2) - Week 11-16
**目标**: 高级分析和工作流功能
- 知识图谱模块 (完整)
- 文本分析模块 (完整)
- 工作流模块 (完整)
- 系统优化和完善

## ⚙️ 技术要求

### 前端技术栈（纯前端Web应用）
- **框架**: React 18 + TypeScript + Vite
- **UI库**: Mantine 7.x (91Writing风格定制)
- **编辑器**: Monaco Editor
- **状态管理**: Zustand + Immer
- **数据获取**: TanStack Query (仅用于AI API调用)
- **本地存储**: IndexedDB + localStorage
- **图谱可视化**: Cytoscape.js + D3.js
- **工作流**: React Flow
- **图标**: Tabler Icons
- **文件处理**: 浏览器File API
- **加密**: Web Crypto API

### 数据存储方案
- **主要存储**: IndexedDB (大量结构化数据)
- **配置存储**: localStorage (用户设置、API密钥等)
- **临时存储**: sessionStorage (会话数据)
- **文件存储**: 浏览器File System Access API (可选)

### AI集成方案
- **API调用**: 直接前端HTTP请求
- **CORS处理**: 代理服务器或CORS扩展
- **流式处理**: Server-Sent Events (SSE) 或 WebSocket
- **错误处理**: 前端重试机制

### 核心依赖
- **前端**: 40+ npm包 (减少后端依赖)
- **数据库**: IndexedDB 本地数据库
- **API接口**: 30+ AI服务商API集成

## 🔗 依赖关系

### 基础依赖链
```
界面系统 → 编辑器模块 → AI集成模块 → 项目管理模块
```

### 高级功能依赖
```
AI集成模块 → 角色管理模块 → 知识库模块
知识库模块 → 知识图谱模块
AI集成模块 → SillyTavern兼容模块
所有模块 → 工作流模块
```

### 关键依赖说明
1. **界面系统**是所有其他模块的基础
2. **AI集成模块**是智能功能的核心依赖（纯前端API调用）
3. **角色管理**依赖于本地文件管理和AI集成
4. **知识图谱**依赖于本地知识库的数据支持
5. **工作流引擎**需要所有其他模块作为节点支持
6. **SillyTavern兼容**需要在AI集成基础上实现（本地扩展支持）

### 5. 知识库模块
#### 5.1 本地知识库系统
- **功能描述**: 文档分块、索引、语义搜索
- **技术要求**: 向量数据库，嵌入模型
- **实现要点**:
  - 文档自动分块和索引
  - 语义相似度搜索
  - 知识库内容管理界面
  - 与编辑器的集成

#### 5.2 聊天向量化扩展
- **功能描述**: 基于SillyTavern Chat Vectorization的聊天历史向量化
- **技术要求**: 向量化算法，相似度计算
- **实现要点**:
  - 聊天历史向量化存储
  - 相关消息检索和注入
  - 向量数据库管理界面
  - 语义搜索优化

#### 5.3 RAG增强系统
- **功能描述**: 检索增强生成，智能上下文注入
- **技术要求**: RAG算法，上下文管理
- **实现要点**:
  - 智能检索相关内容
  - 上下文相关性评分
  - 动态上下文窗口调整
  - RAG效果评估

#### 5.4 记忆管理系统
- **功能描述**: 基于st-memory-enhancement的表格化记忆管理
- **技术要求**: 记忆存储，检索算法
- **实现要点**:
  - 表格化记忆存储
  - 智能记忆检索
  - 记忆压缩和优化
  - 记忆关联分析

### 6. 知识图谱模块
#### 6.1 本地知识图谱系统
- **功能描述**: 本地知识图谱构建和管理（移除MCP集成）
- **技术要求**: 前端图谱算法，IndexedDB存储
- **实现要点**:
  - 本地图谱数据结构
  - 图谱数据本地存储
  - 本地缓存管理
  - 增量更新支持

#### 6.2 图谱可视化系统
- **功能描述**: 交互式图谱浏览器，多维度视图
- **技术要求**: Cytoscape.js，前端图谱算法
- **实现要点**:
  - 交互式图谱浏览
  - 多种布局算法
  - 节点和边的编辑
  - 图谱搜索和过滤

#### 6.3 人物关系分析
- **功能描述**: 本地人物关系分析算法
- **技术要求**: 前端关系抽取，图谱分析
- **实现要点**:
  - 人物关系自动抽取
  - 关系强度计算
  - 关系网络分析
  - 关系图谱本地导出

### 7. 文本分析模块
#### 7.1 雷点检测系统
- **功能描述**: 基于标准定义的雷点检测，智能改编建议
- **技术要求**: NLP算法，规则引擎
- **实现要点**:
  - 多维度雷点检测
  - 检测结果可视化
  - 改编建议生成
  - 检测规则自定义

#### 7.2 批量处理引擎
- **功能描述**: 三种处理模式，支持大规模文本处理
- **技术要求**: 并发处理，任务调度
- **实现要点**:
  - 严格串行、并行、无PSKB三种模式
  - 批量任务管理
  - 进度监控和状态恢复
  - 失败重试机制

#### 7.3 文本质量评估
- **功能描述**: 多维度文本质量分析和评分
- **技术要求**: 质量评估算法，统计分析
- **实现要点**:
  - 文本可读性分析
  - 情节连贯性检测
  - 角色一致性验证
  - 质量报告生成

#### 7.4 一键拆书功能
- **功能描述**: 智能章节分割，原文反推
- **技术要求**: 文本分割算法，逆向分析
- **实现要点**:
  - 智能章节边界识别
  - 原文结构分析
  - 改编痕迹检测
  - 拆书结果导出

### 8. 工作流模块
#### 8.1 可视化节点编辑器
- **功能描述**: 基于React Flow的拖拽式节点编辑器
- **技术要求**: React Flow，节点系统
- **实现要点**:
  - 拖拽式节点编辑
  - 节点连接和配置
  - 工作流预览和验证
  - 节点库管理

#### 8.2 工作流执行引擎
- **功能描述**: 复杂任务调度，异步执行
- **技术要求**: 任务调度，状态管理
- **实现要点**:
  - 工作流解析和执行
  - 异步任务调度
  - 执行状态监控
  - 错误处理和重试

#### 8.3 预置节点库
- **功能描述**: 丰富的预置处理节点，支持扩展
- **技术要求**: 节点抽象，插件系统
- **实现要点**:
  - AI处理节点
  - 文本分析节点
  - 数据转换节点
  - 自定义节点支持

#### 8.4 工作流模板系统
- **功能描述**: 工作流模板管理，社区分享
- **技术要求**: 模板存储，版本管理
- **实现要点**:
  - 模板创建和编辑
  - 模板导入导出
  - 社区模板分享
  - 模板版本管理

### 9. SillyTavern兼容模块
#### 9.1 扩展系统兼容
- **功能描述**: 完整的SillyTavern扩展系统兼容
- **技术要求**: 扩展加载，API兼容
- **实现要点**:
  - manifest.json解析
  - 扩展生命周期管理
  - 扩展沙箱环境
  - 扩展依赖管理

#### 9.2 API兼容层
- **功能描述**: 完整的SillyTavern API兼容
- **技术要求**: API映射，事件系统
- **实现要点**:
  - getContext()全局对象
  - 核心库提供(lodash、localforage等)
  - 事件系统兼容
  - 斜杠命令系统

#### 9.3 数据格式兼容
- **功能描述**: 支持SillyTavern所有数据格式
- **技术要求**: 数据解析，格式转换
- **实现要点**:
  - 角色卡V2格式
  - 世界书格式
  - 聊天历史格式
  - 设置预设格式

#### 9.4 本地扩展管理
- **功能描述**: 本地扩展安装、管理（移除扩展商店）
- **技术要求**: 本地包管理，文件处理
- **实现要点**:
  - 本地扩展文件管理
  - 手动安装和卸载
  - 扩展配置管理
  - 本地使用统计

### 10. 项目管理模块
#### 10.1 项目创建管理
- **功能描述**: 项目创建、打开、保存，模板支持
- **技术要求**: 文件系统，项目模板
- **实现要点**:
  - 项目向导和模板
  - 项目结构管理
  - 项目设置配置
  - 项目状态保存

#### 10.2 文件组织系统
- **功能描述**: 智能文件组织，章节管理
- **技术要求**: 文件系统API，元数据管理
- **实现要点**:
  - 文件夹结构管理
  - 章节自动排序
  - 文件标签和分类
  - 文件搜索功能

#### 10.3 本地备份功能
- **功能描述**: 本地自动备份（移除云同步）
- **技术要求**: 本地备份策略，文件管理
- **实现要点**:
  - 本地自动备份机制
  - 版本历史管理
  - 本地存储管理
  - 备份恢复机制

#### 10.4 导入导出系统
- **功能描述**: 多格式导入导出，批量处理
- **技术要求**: 格式转换，批量处理
- **实现要点**:
  - 多种文件格式支持
  - 批量导入导出
  - 格式转换优化
  - 导出模板定制

## 📊 项目规模评估
- **预计代码量**: 35,000-40,000 行（纯前端项目）
- **开发周期**: 12-16周（移除后端开发）
- **团队规模**: 建议2-3人（前端开发者）
- **技术难度**: 大型前端项目
- **风险等级**: 中等风险，技术栈相对简单
- **部署方式**: 静态网站部署，支持CDN加速

## 🎯 开发里程碑

### 里程碑1 (Week 4): 基础架构完成
- ✅ 界面系统模块完整实现（91Writing风格）
- ✅ 编辑器模块基础功能（Monaco Editor集成）
- ✅ AI集成模块基础功能（多AI API支持）
- ✅ 项目管理模块基础功能（本地文件管理）

### 里程碑2 (Week 7): 核心AI功能完成
- ✅ AI集成模块高级功能（智能提示词、参数配置）
- ✅ 知识库模块基础功能（本地向量搜索）
- ✅ SillyTavern兼容模块基础（API兼容层）
- ✅ 角色管理模块基础功能（角色卡解析）

### 里程碑3 (Week 10): 完整功能系统
- ✅ 角色管理模块完整实现（关系图谱、世界书）
- ✅ 知识库模块完整实现（RAG增强、记忆管理）
- ✅ SillyTavern兼容模块完整（本地扩展支持）
- ✅ 系统优化和性能提升

### 里程碑4 (Week 13): 高级分析功能
- ✅ 知识图谱模块完整实现（本地图谱系统）
- ✅ 文本分析模块完整实现（雷点检测、质量评估）
- ✅ 批量处理引擎完成（前端并发处理）
- ✅ 高级功能集成测试

### 里程碑5 (Week 16): 产品发布就绪
- ✅ 工作流模块完整实现（可视化节点编辑器）
- ✅ 项目管理模块完善（本地备份、导入导出）
- ✅ 全面测试和优化（性能优化、兼容性测试）
- ✅ 文档和发布准备（静态网站部署）

## ⚠️ 风险评估

### 技术风险
- **浏览器兼容性**: 需要确保现代浏览器的兼容性
- **本地存储限制**: IndexedDB存储容量和性能限制
- **CORS问题**: AI API调用的跨域问题
- **前端性能**: 大量数据处理的前端性能挑战

### 进度风险
- **功能范围**: 虽然移除了后端，但前端功能仍然复杂
- **AI API集成**: 多种AI服务的API差异和调试
- **SillyTavern兼容**: 前端扩展系统的实现难度
- **测试覆盖**: 纯前端应用的测试策略

### 资源风险
- **前端开发**: 需要经验丰富的React/TypeScript开发者
- **AI集成专家**: 需要熟悉各种AI API的开发者
- **第三方依赖**: 前端库的稳定性和更新
- **浏览器限制**: 本地存储和文件处理的浏览器限制

## 🎯 成功标准

### 功能完整性
- 实现所有核心功能模块（纯前端实现）
- SillyTavern本地兼容（移除在线功能）
- 91Writing界面风格一致性≥90%
- 支持本地扩展正常运行

### 性能指标
- 应用加载时间≤3秒（首次访问）
- 编辑器响应时间≤50ms
- AI生成响应时间≤10秒（取决于API）
- 本地文件处理能力≥5MB

### 用户体验
- 界面友好，操作直观
- 学习成本低，5分钟上手
- 错误提示清晰友好
- 离线功能支持

### 稳定性要求
- 浏览器长时间运行稳定
- 本地数据安全可靠
- 自动保存到本地存储
- 完善的错误边界处理

### 扩展性标准
- 模块化前端架构
- 本地扩展系统
- 组件文档完整
- 易于二次开发

## 🚀 部署和发布

### 部署方案
- **静态网站托管**: Vercel、Netlify、GitHub Pages
- **CDN加速**: 全球内容分发网络
- **PWA支持**: 渐进式Web应用，支持离线使用
- **自动化部署**: CI/CD流水线，自动构建和发布

### 浏览器支持
- **Chrome**: 90+ (推荐)
- **Firefox**: 88+
- **Safari**: 14+
- **Edge**: 90+

### 系统要求
- **内存**: 建议4GB以上
- **存储**: 本地存储空间500MB以上
- **网络**: 仅AI API调用需要网络连接
- **设备**: 支持桌面和平板设备

---
*本功能需求清单基于AI小说IDE项目详细执行计划调整而成，专门针对纯前端Web应用架构优化。移除了所有后端、社区和云服务功能，专注于个人使用场景的本地化AI写作工具。建议严格按照优先级和里程碑进行开发，确保项目按时高质量交付。*
