# AI小说IDE 功能需求清单

## 📋 项目概述
**项目名称**: AI小说IDE (NovelCraft AI Studio)  
**技术栈**: Tauri 2.0 + React 18 + TypeScript + Vite + Mantine  
**界面设计**: 基于91Writing项目的现代化界面风格  
**开发周期**: 20-24周  
**项目复杂度**: 超大型复杂项目（集成7大核心系统）

## 🎯 核心功能模块

### 1. 界面系统模块 (UI System)
**描述**: 基于91Writing风格的现代化界面系统，提供5个专业工作区
**优先级**: 🔴 高优先级 (P0)

### 2. 编辑器模块 (Editor System)  
**描述**: 基于Monaco Editor的智能小说编辑器，支持AI辅助写作
**优先级**: 🔴 高优先级 (P0)

### 3. AI集成模块 (AI Integration)
**描述**: 多AI模型集成，支持30+AI服务商，智能提示词系统
**优先级**: 🔴 高优先级 (P0)

### 4. 角色管理模块 (Character Management)
**描述**: 完整的角色管理系统，支持SillyTavern角色卡格式
**优先级**: 🟡 中优先级 (P1)

### 5. 知识库模块 (Knowledge Base)
**描述**: 本地向量搜索 + RAG增强 + 记忆管理系统
**优先级**: 🟡 中优先级 (P1)

### 6. 知识图谱模块 (Knowledge Graph)
**描述**: Graphiti MCP集成 + 可视化图谱系统
**优先级**: 🟢 低优先级 (P2)

### 7. 文本分析模块 (Text Analysis)
**描述**: 雷点检测 + 改编策略 + 批量处理引擎
**优先级**: 🟢 低优先级 (P2)

### 8. 工作流模块 (Workflow Engine)
**描述**: 可视化节点编辑器 + 复杂任务调度系统
**优先级**: 🟢 低优先级 (P2)

### 9. SillyTavern兼容模块 (ST Compatibility)
**描述**: 完整SillyTavern扩展生态兼容，支持50+扩展
**优先级**: 🟡 中优先级 (P1)

### 10. 项目管理模块 (Project Management)
**描述**: 项目创建、文件组织、备份同步、导入导出
**优先级**: 🔴 高优先级 (P0)

## 📝 具体功能点

### 1. 界面系统模块
#### 1.1 91Writing风格主题系统
- **功能描述**: 完整映射91Writing色彩系统和设计规范
- **技术要求**: Mantine主题深度定制，CSS变量管理
- **实现要点**: 
  - 主色调#409eff，成功色#67c23a，警告色#e6a23c，危险色#f56c6c
  - 文本色阶：#303133(主要)、#606266(常规)、#909399(次要)
  - 背景色：#f5f7fa(页面)、#fafafa(浅色)
  - 6px圆角，统一间距系统(8px倍数)

#### 1.2 AppShell主布局系统
- **功能描述**: 顶部导航(60px) + 三栏式主体布局(320px + 自适应 + 320px)
- **技术要求**: Mantine AppShell组件，响应式设计
- **实现要点**:
  - 顶部导航：应用标题 + 工作区切换 + 功能按钮
  - 左侧面板：功能设置、文件树、工具面板
  - 中间区域：编辑器、可视化区、配置表单
  - 右侧面板：AI助手、聊天面板、结果展示

#### 1.3 五个工作区系统
- **功能描述**: 编辑器、角色管理、知识图谱、项目管理、设置配置工作区
- **技术要求**: React组件化设计，状态管理，路由切换
- **实现要点**:
  - 工作区切换机制(Ctrl+1-5快捷键)
  - 状态保持和恢复
  - 平滑切换动画效果

#### 1.4 基础组件库
- **功能描述**: WorkspaceCard、ToolbarButton、StatusIndicator、ChatPanel等
- **技术要求**: TypeScript类型定义，Mantine组件扩展
- **实现要点**:
  - 统一的视觉风格和交互规范
  - 可复用的组件设计
  - 完整的属性接口定义

### 2. 编辑器模块
#### 2.1 Monaco Editor集成
- **功能描述**: 专业代码编辑器集成，支持小说写作优化
- **技术要求**: @monaco-editor/react，自定义语言支持
- **实现要点**:
  - 91Writing风格主题配置
  - 小说专用语法高亮
  - 智能提示和自动补全
  - 多标签页支持

#### 2.2 智能续写功能
- **功能描述**: Ctrl+J快捷键触发AI续写，多种生成模式
- **技术要求**: AI API集成，流式输出处理
- **实现要点**:
  - 续写、改写、扩展、总结四种模式
  - 实时流式输出显示
  - 生成状态指示和进度显示
  - 生成结果的接受/拒绝操作

#### 2.3 文件管理系统
- **功能描述**: 左侧文件树，支持新建、删除、重命名等操作
- **技术要求**: Mantine Tree组件，文件系统API
- **实现要点**:
  - 91Writing风格的文件树设计
  - 章节管理和自动排序
  - 文件拖拽操作
  - 自动保存和恢复机制

#### 2.4 编辑器工具栏
- **功能描述**: 参考91Writing的编辑器工具栏设计
- **技术要求**: Mantine Button组件，图标库
- **实现要点**:
  - 字数统计、预计阅读时间显示
  - 生成、导出、清空等功能按钮
  - 编辑器设置快捷入口
  - 状态栏信息显示

### 3. AI集成模块
#### 3.1 多AI模型支持
- **功能描述**: 支持OpenAI、Claude、DeepSeek、Groq、MistralAI等30+AI服务商
- **技术要求**: 统一API抽象层，适配器模式
- **实现要点**:
  - 统一的API调用封装
  - 参数映射和格式转换
  - 热切换机制
  - 负载均衡和故障转移

#### 3.2 API密钥管理
- **功能描述**: 密钥池管理，轮询机制，使用统计
- **技术要求**: 加密存储，安全管理
- **实现要点**:
  - 多密钥轮询使用
  - 使用量统计和限制
  - 密钥有效性检测
  - 安全的本地存储

#### 3.3 高级参数配置
- **功能描述**: temperature、top_p、top_k等完整参数支持
- **技术要求**: 参数验证，预设管理
- **实现要点**:
  - 参数范围验证和类型检查
  - 预设的导入导出功能
  - 社区预设分享
  - 实时参数调整

#### 3.4 智能提示词系统
- **功能描述**: 动态提示词模板，变量替换，条件逻辑
- **技术要求**: 模板引擎，版本管理
- **实现要点**:
  - 动态变量替换
  - 模板版本管理和回滚
  - 社区模板分享
  - 提示词拦截器系统

### 4. 角色管理模块
#### 4.1 角色卡解析导入
- **功能描述**: 支持PNG角色卡解析，兼容SillyTavern V2格式
- **技术要求**: PNG元数据解析，JSON格式处理
- **实现要点**:
  - PNG嵌入式角色卡解析
  - SillyTavern V2格式完全兼容
  - 批量导入功能
  - 角色卡验证和修复

#### 4.2 角色编辑器
- **功能描述**: 完整的角色属性编辑，支持Name、Description、Personality等
- **技术要求**: 表单组件，数据验证
- **实现要点**:
  - 所有SillyTavern字段支持
  - 实时预览功能
  - 角色头像管理
  - 属性模板系统

#### 4.3 角色关系图谱
- **功能描述**: 可视化角色关系，支持多种关系类型
- **技术要求**: Cytoscape.js，图谱算法
- **实现要点**:
  - 交互式关系编辑
  - 关系类型定义
  - 图谱布局算法
  - 关系分析统计

#### 4.4 世界书系统
- **功能描述**: 兼容SillyTavern World Info，支持关键词激活
- **技术要求**: 关键词匹配，逻辑判断
- **实现要点**:
  - 关键词匹配算法
  - AND、OR、NOT逻辑支持
  - 条目激活历史
  - 递归扫描机制

## 🎯 优先级分类

### 🔴 高优先级 (P0) - Week 1-4
**目标**: 基础可用的小说编辑器
- 界面系统模块 (完整)
- 编辑器模块 (基础功能)
- AI集成模块 (基础功能)
- 项目管理模块 (基础功能)

### 🟡 中优先级 (P1) - Week 5-12  
**目标**: 完整的AI辅助写作系统
- 角色管理模块 (完整)
- 知识库模块 (完整)
- SillyTavern兼容模块 (完整)
- AI集成模块 (高级功能)

### 🟢 低优先级 (P2) - Week 13-20
**目标**: 高级分析和工作流功能
- 知识图谱模块 (完整)
- 文本分析模块 (完整)
- 工作流模块 (完整)
- 系统优化和完善

## ⚙️ 技术要求

### 前端技术栈
- **框架**: React 18 + TypeScript + Vite
- **UI库**: Mantine 7.x (91Writing风格定制)
- **编辑器**: Monaco Editor
- **状态管理**: Zustand + Immer
- **数据获取**: TanStack Query
- **图谱可视化**: Cytoscape.js + D3.js
- **工作流**: React Flow
- **图标**: Tabler Icons

### 后端技术栈
- **框架**: Tauri 2.0 + Rust
- **数据库**: SQLite (主) + RocksDB (可选)
- **网络**: Reqwest + Tokio + WebSocket
- **AI集成**: 多种AI API适配器
- **文本处理**: Jieba-rs + Regex
- **并发**: Rayon + Tokio
- **序列化**: Serde + Bincode

### 核心依赖
- **前端**: 70+ npm包
- **后端**: 30+ Rust crates
- **数据库**: 20+ 复杂表结构
- **API接口**: 150+ RESTful + WebSocket

## 🔗 依赖关系

### 基础依赖链
```
界面系统 → 编辑器模块 → AI集成模块 → 项目管理模块
```

### 高级功能依赖
```
AI集成模块 → 角色管理模块 → 知识库模块
知识库模块 → 知识图谱模块
AI集成模块 → SillyTavern兼容模块
所有模块 → 工作流模块
```

### 关键依赖说明
1. **界面系统**是所有其他模块的基础
2. **AI集成模块**是智能功能的核心依赖
3. **角色管理**依赖于文件管理和AI集成
4. **知识图谱**依赖于知识库的数据支持
5. **工作流引擎**需要所有其他模块作为节点支持
6. **SillyTavern兼容**需要在AI集成基础上实现

### 5. 知识库模块
#### 5.1 本地知识库系统
- **功能描述**: 文档分块、索引、语义搜索
- **技术要求**: 向量数据库，嵌入模型
- **实现要点**:
  - 文档自动分块和索引
  - 语义相似度搜索
  - 知识库内容管理界面
  - 与编辑器的集成

#### 5.2 聊天向量化扩展
- **功能描述**: 基于SillyTavern Chat Vectorization的聊天历史向量化
- **技术要求**: 向量化算法，相似度计算
- **实现要点**:
  - 聊天历史向量化存储
  - 相关消息检索和注入
  - 向量数据库管理界面
  - 语义搜索优化

#### 5.3 RAG增强系统
- **功能描述**: 检索增强生成，智能上下文注入
- **技术要求**: RAG算法，上下文管理
- **实现要点**:
  - 智能检索相关内容
  - 上下文相关性评分
  - 动态上下文窗口调整
  - RAG效果评估

#### 5.4 记忆管理系统
- **功能描述**: 基于st-memory-enhancement的表格化记忆管理
- **技术要求**: 记忆存储，检索算法
- **实现要点**:
  - 表格化记忆存储
  - 智能记忆检索
  - 记忆压缩和优化
  - 记忆关联分析

### 6. 知识图谱模块
#### 6.1 Graphiti MCP集成
- **功能描述**: 集成Graphiti MCP客户端，实现图谱数据同步
- **技术要求**: MCP协议，WebSocket通信
- **实现要点**:
  - MCP协议通信层
  - 图谱数据同步机制
  - 本地缓存管理
  - 增量更新支持

#### 6.2 图谱可视化系统
- **功能描述**: 交互式图谱浏览器，多维度视图
- **技术要求**: Cytoscape.js，图谱算法
- **实现要点**:
  - 交互式图谱浏览
  - 多种布局算法
  - 节点和边的编辑
  - 图谱搜索和过滤

#### 6.3 笔墨星河人物关系
- **功能描述**: 集成笔墨星河人物关系分析算法
- **技术要求**: 关系抽取，图谱分析
- **实现要点**:
  - 人物关系自动抽取
  - 关系强度计算
  - 关系网络分析
  - 关系图谱导出

### 7. 文本分析模块
#### 7.1 雷点检测系统
- **功能描述**: 基于标准定义的雷点检测，智能改编建议
- **技术要求**: NLP算法，规则引擎
- **实现要点**:
  - 多维度雷点检测
  - 检测结果可视化
  - 改编建议生成
  - 检测规则自定义

#### 7.2 批量处理引擎
- **功能描述**: 三种处理模式，支持大规模文本处理
- **技术要求**: 并发处理，任务调度
- **实现要点**:
  - 严格串行、并行、无PSKB三种模式
  - 批量任务管理
  - 进度监控和状态恢复
  - 失败重试机制

#### 7.3 文本质量评估
- **功能描述**: 多维度文本质量分析和评分
- **技术要求**: 质量评估算法，统计分析
- **实现要点**:
  - 文本可读性分析
  - 情节连贯性检测
  - 角色一致性验证
  - 质量报告生成

#### 7.4 一键拆书功能
- **功能描述**: 智能章节分割，原文反推
- **技术要求**: 文本分割算法，逆向分析
- **实现要点**:
  - 智能章节边界识别
  - 原文结构分析
  - 改编痕迹检测
  - 拆书结果导出

### 8. 工作流模块
#### 8.1 可视化节点编辑器
- **功能描述**: 基于React Flow的拖拽式节点编辑器
- **技术要求**: React Flow，节点系统
- **实现要点**:
  - 拖拽式节点编辑
  - 节点连接和配置
  - 工作流预览和验证
  - 节点库管理

#### 8.2 工作流执行引擎
- **功能描述**: 复杂任务调度，异步执行
- **技术要求**: 任务调度，状态管理
- **实现要点**:
  - 工作流解析和执行
  - 异步任务调度
  - 执行状态监控
  - 错误处理和重试

#### 8.3 预置节点库
- **功能描述**: 丰富的预置处理节点，支持扩展
- **技术要求**: 节点抽象，插件系统
- **实现要点**:
  - AI处理节点
  - 文本分析节点
  - 数据转换节点
  - 自定义节点支持

#### 8.4 工作流模板系统
- **功能描述**: 工作流模板管理，社区分享
- **技术要求**: 模板存储，版本管理
- **实现要点**:
  - 模板创建和编辑
  - 模板导入导出
  - 社区模板分享
  - 模板版本管理

### 9. SillyTavern兼容模块
#### 9.1 扩展系统兼容
- **功能描述**: 完整的SillyTavern扩展系统兼容
- **技术要求**: 扩展加载，API兼容
- **实现要点**:
  - manifest.json解析
  - 扩展生命周期管理
  - 扩展沙箱环境
  - 扩展依赖管理

#### 9.2 API兼容层
- **功能描述**: 完整的SillyTavern API兼容
- **技术要求**: API映射，事件系统
- **实现要点**:
  - getContext()全局对象
  - 核心库提供(lodash、localforage等)
  - 事件系统兼容
  - 斜杠命令系统

#### 9.3 数据格式兼容
- **功能描述**: 支持SillyTavern所有数据格式
- **技术要求**: 数据解析，格式转换
- **实现要点**:
  - 角色卡V2格式
  - 世界书格式
  - 聊天历史格式
  - 设置预设格式

#### 9.4 扩展商店系统
- **功能描述**: 扩展浏览、安装、更新管理
- **技术要求**: 包管理，自动更新
- **实现要点**:
  - 扩展搜索和浏览
  - 一键安装和更新
  - 扩展配置管理
  - 使用统计和评价

### 10. 项目管理模块
#### 10.1 项目创建管理
- **功能描述**: 项目创建、打开、保存，模板支持
- **技术要求**: 文件系统，项目模板
- **实现要点**:
  - 项目向导和模板
  - 项目结构管理
  - 项目设置配置
  - 项目状态保存

#### 10.2 文件组织系统
- **功能描述**: 智能文件组织，章节管理
- **技术要求**: 文件系统API，元数据管理
- **实现要点**:
  - 文件夹结构管理
  - 章节自动排序
  - 文件标签和分类
  - 文件搜索功能

#### 10.3 备份同步功能
- **功能描述**: 自动备份，云同步支持
- **技术要求**: 备份策略，同步协议
- **实现要点**:
  - 自动备份机制
  - 版本历史管理
  - 云存储集成
  - 冲突解决机制

#### 10.4 导入导出系统
- **功能描述**: 多格式导入导出，批量处理
- **技术要求**: 格式转换，批量处理
- **实现要点**:
  - 多种文件格式支持
  - 批量导入导出
  - 格式转换优化
  - 导出模板定制

## 📊 项目规模评估
- **预计代码量**: 60,000+ 行
- **开发周期**: 20-24周
- **团队规模**: 建议3-5人
- **技术难度**: 超大型复杂项目
- **风险等级**: 高风险，需要充分的时间和资源投入

## 🎯 开发里程碑

### 里程碑1 (Week 4): 基础架构完成
- ✅ 界面系统模块完整实现
- ✅ 编辑器模块基础功能
- ✅ AI集成模块基础功能
- ✅ 项目管理模块基础功能

### 里程碑2 (Week 8): 核心AI功能完成
- ✅ AI集成模块高级功能
- ✅ 知识库模块基础功能
- ✅ SillyTavern兼容模块基础
- ✅ 角色管理模块基础功能

### 里程碑3 (Week 12): 完整功能系统
- ✅ 角色管理模块完整实现
- ✅ 知识库模块完整实现
- ✅ SillyTavern兼容模块完整
- ✅ 系统优化和性能提升

### 里程碑4 (Week 16): 高级分析功能
- ✅ 知识图谱模块完整实现
- ✅ 文本分析模块完整实现
- ✅ 批量处理引擎完成
- ✅ 高级功能集成测试

### 里程碑5 (Week 20): 产品发布就绪
- ✅ 工作流模块完整实现
- ✅ 项目管理模块完善
- ✅ 全面测试和优化
- ✅ 文档和发布准备

## ⚠️ 风险评估

### 技术风险
- **Tauri学习曲线**: 需要时间学习Rust和Tauri框架
- **复杂功能集成**: 多系统集成可能遇到兼容性问题
- **性能优化**: 大规模数据处理的性能挑战
- **SillyTavern兼容**: 扩展系统兼容性的技术难度

### 进度风险
- **功能范围过大**: 可能导致开发周期延长
- **技术难点**: 某些功能实现比预期复杂
- **依赖关系**: 模块间依赖可能影响并行开发
- **测试复杂度**: 复杂系统的测试工作量大

### 资源风险
- **开发人力**: 需要充足的开发人员
- **技术专家**: 需要AI、图谱、NLP等领域专家
- **第三方依赖**: 依赖的开源项目可能存在问题
- **硬件资源**: AI模型和大数据处理的硬件需求

## 🎯 成功标准

### 功能完整性
- 实现所有核心功能模块
- SillyTavern完全兼容
- 91Writing界面风格一致性≥90%
- 支持50+扩展正常运行

### 性能指标
- 应用启动时间≤2秒
- 编辑器响应时间≤50ms
- AI生成响应时间≤5秒
- 大文件处理能力≥10MB

### 用户体验
- 界面友好，操作直观
- 学习成本低，5分钟上手
- 错误提示清晰友好
- 功能发现性良好

### 稳定性要求
- 长时间运行无崩溃
- 数据安全可靠
- 自动保存和恢复
- 异常处理完善

### 扩展性标准
- 架构清晰，便于扩展
- 插件系统完善
- API文档完整
- 社区生态支持

---
*本功能需求清单基于AI小说IDE项目详细执行计划提炼而成，为后续开发规划和任务分配提供参考依据。建议严格按照优先级和里程碑进行开发，确保项目按时高质量交付。*
